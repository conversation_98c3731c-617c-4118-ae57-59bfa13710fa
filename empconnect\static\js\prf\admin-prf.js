class AdminPRFDashboard {
    constructor() {
        this.chart = null;
        this.currentChartType = 'line';
        this.currentPeriod = 'month';
        this.selectedPRFs = new Set();
        this.processingPRFId = null;
        this.isUpdatingChart = false;
        this.init();
    }

    init() {
        this.initChart();
        this.setupEventListeners();
        this.setupSearch();
        this.setupAnimations();
        this.initFilterSlider();
    }

    initChart() {
        const ctx = document.getElementById('prfChart');
        if (!ctx || !window.chartData) return;

        const data = window.chartData;
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        
        this.createChart(ctx, data, isDark);
    }

    createChart(ctx, data, isDark) {
        const textColor = isDark ? '#f8fafc' : '#0f172a';
        const gridColor = isDark ? '#334155' : '#e2e8f0';

        if (this.chart) {
            this.chart.destroy();
        }

        // Define category colors
        const categoryColors = {
            government: '#6366f1',
            banking: '#10b981',
            hr_payroll: '#f59e0b'
        };

        const categoryLabels = {
            government: 'Government Transaction',
            banking: 'Banking and Finance',
            hr_payroll: 'Human Resources and Payroll'
        };

        // Create datasets for each category
        const datasets = [];
        const categories = ['government', 'banking', 'hr_payroll'];

        categories.forEach(category => {
            datasets.push({
                label: categoryLabels[category],
                data: data.map(item => item[category] || 0),
                borderColor: categoryColors[category],
                backgroundColor: this.currentChartType === 'line'
                    ? `${categoryColors[category]}20`
                    : categoryColors[category],
                tension: this.currentChartType === 'line' ? 0.4 : 0,
                fill: this.currentChartType === 'line',
                pointRadius: this.currentChartType === 'line' ? 4 : 0,
                pointHoverRadius: this.currentChartType === 'line' ? 6 : 0,
                pointBackgroundColor: categoryColors[category],
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                borderRadius: this.currentChartType === 'bar' ? 6 : 0,
                borderSkipped: false,
            });
        });

        const chartData = {
            labels: data.map(item => item.date),
            datasets: datasets
        };

        this.chart = new Chart(ctx, {
            type: this.currentChartType,
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        top: 20,
                        right: 30,
                        bottom: 20,
                        left: 30
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: textColor,
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: isDark ? '#1e293b' : '#ffffff',
                        titleColor: textColor,
                        bodyColor: textColor,
                        borderColor: isDark ? '#334155' : '#e2e8f0',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                return `${context.dataset.label}: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColor,
                            font: {
                                size: 12
                            }
                        },
                        grid: {
                            color: gridColor,
                            drawBorder: false,
                        },
                        stacked: this.currentChartType === 'bar'
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: textColor,
                            font: {
                                size: 12
                            },
                            precision: 0
                        },
                        grid: {
                            color: gridColor,
                            drawBorder: false,
                        },
                        stacked: this.currentChartType === 'bar'
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    initFilterSlider() {
        const filterContainer = document.querySelector('.chart-filters');
        const filterButtons = filterContainer?.querySelectorAll('.filter-btn');

        if (!filterContainer || !filterButtons.length) return;

        // Set initial active state
        const activeButton = filterContainer.querySelector('.filter-btn.active');
        if (activeButton) {
            const index = Array.from(filterButtons).indexOf(activeButton);
            filterContainer.classList.add('has-active', `slide-${index}`);
        }
    }

    async updateChartPeriod(period) {
        if (this.isUpdatingChart) return;

        this.isUpdatingChart = true;
        this.currentPeriod = period;

        // Update filter button states and slide effect
        const filterContainer = document.querySelector('.chart-filters');
        const filterButtons = filterContainer?.querySelectorAll('.filter-btn');

        if (filterContainer && filterButtons) {
            // Remove all slide classes
            filterContainer.classList.remove('slide-0', 'slide-1', 'slide-2', 'has-active');

            // Update active states
            filterButtons.forEach((btn, index) => {
                const btnPeriod = btn.onclick.toString().match(/'([\w]+)'/)?.[1];
                if (btnPeriod === period) {
                    btn.classList.add('active');
                    filterContainer.classList.add('has-active', `slide-${index}`);
                } else {
                    btn.classList.remove('active');
                }
            });
        }

        try {
            // Fetch new chart data via AJAX
            const response = await fetch(`/prf/hradmin/chart-data/?period=${period}`);
            const result = await response.json();

            this.updateChartData(result.data, document.documentElement.getAttribute('data-theme') === 'dark');
        } catch (error) {
            this.showToast('Failed to update chart data', 'error');
        } finally {
            this.isUpdatingChart = false;
        }
    }

    updateChartData(data, isDark) {
        if (!this.chart) return;

        // Define category colors
        const categoryColors = {
            government: '#6366f1',
            banking: '#10b981',
            hr_payroll: '#f59e0b'
        };

        const categoryLabels = {
            government: 'Government Transaction',
            banking: 'Banking and Finance',
            hr_payroll: 'Human Resources and Payroll'
        };

        // Update chart data
        this.chart.data.labels = data.map(item => item.date);

        // Update datasets
        const categories = ['government', 'banking', 'hr_payroll'];
        categories.forEach((category, index) => {
            if (this.chart.data.datasets[index]) {
                this.chart.data.datasets[index].data = data.map(item => item[category] || 0);
                this.chart.data.datasets[index].backgroundColor = this.currentChartType === 'line'
                    ? `${categoryColors[category]}20`
                    : categoryColors[category];
            }
        });

        // Update chart options for stacked bars
        this.chart.options.scales.x.stacked = this.currentChartType === 'bar';
        this.chart.options.scales.y.stacked = this.currentChartType === 'bar';

        this.chart.update('active');
    }

    setupEventListeners() {
        document.getElementById('exportForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.exportPRFs();
        });

        document.getElementById('processForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.processPRFRequest();
        });

        document.getElementById('filterForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.applyFilters();
        });

        document.getElementById('selectAllCheckbox')?.addEventListener('change', (e) => {
            this.selectAllCheckboxes(e.target.checked);
        });

        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 500);
            });
        }
    }

    setupSearch() {
        const searchInput = document.getElementById('searchInput');
        const searchClear = document.querySelector('.search-clear');
        
        if (searchClear) {
            searchClear.addEventListener('click', () => {
                searchInput.value = '';
                this.performSearch('');
                searchInput.focus();
            });
        }
    }

    setupAnimations() {
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        const tableRows = document.querySelectorAll('.data-table tbody tr');
        tableRows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                row.style.transition = 'all 0.4s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, (index * 50) + 300);
        });
    }

    performSearch(query) {
        const url = new URL(window.location);
        if (query.trim()) {
            url.searchParams.set('search', query);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.delete('page');
        window.location.href = url.toString();
    }

    applyFilters() {
        const form = document.getElementById('filterForm');
        const formData = new FormData(form);
        const url = new URL(window.location);
        
        for (const [key, value] of formData.entries()) {
            if (value.trim()) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
        }
        
        url.searchParams.delete('page');
        closeModal('filterModal');
        window.location.href = url.toString();
    }

    clearFilters() {
        const url = new URL(window.location);
        const searchParam = url.searchParams.get('search');
        
        url.search = '';
        if (searchParam) {
            url.searchParams.set('search', searchParam);
        }
        
        window.location.href = url.toString();
    }

    selectAllCheckboxes(checked) {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            if (checked) {
                this.selectedPRFs.add(checkbox.value);
            } else {
                this.selectedPRFs.delete(checkbox.value);
            }
        });
        this.updateBulkDeleteButton();
    }

    updateBulkDeleteButton() {
        const checkboxes = document.querySelectorAll('.row-checkbox:checked');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        
        this.selectedPRFs.clear();
        checkboxes.forEach(checkbox => {
            this.selectedPRFs.add(checkbox.value);
        });

        if (this.selectedPRFs.size > 0) {
            bulkDeleteBtn.style.display = 'inline-flex';
            bulkDeleteBtn.textContent = `Delete Selected (${this.selectedPRFs.size})`;
        } else {
            bulkDeleteBtn.style.display = 'none';
        }

        const allCheckboxes = document.querySelectorAll('.row-checkbox');
        selectAllCheckbox.checked = checkboxes.length === allCheckboxes.length && allCheckboxes.length > 0;
        selectAllCheckbox.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
    }

    async exportPRFs() {
        const form = document.getElementById('exportForm');
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<span class="loading-spinner"></span> Exporting...';
        submitBtn.disabled = true;

        try {
            const response = await fetch(`/prf/admin/export/?${params.toString()}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')?.split('filename=')[1] || 'prf_requests.xlsx';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showToast('PRF requests exported successfully!', 'success');
                closeModal('exportModal');
            } else {
                this.showToast('Error exporting PRF requests', 'error');
            }
        } catch (error) {
            console.error('Error exporting PRFs:', error);
            this.showToast('Error exporting PRF requests', 'error');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async processPRFRequest() {
        if (!this.processingPRFId) return;

        const form = document.getElementById('processForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        submitBtn.innerHTML = '<span class="loading-spinner"></span> Processing...';
        submitBtn.disabled = true;

        try {
            const response = await fetch(`/prf/admin/process/${this.processingPRFId}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showToast(data.message, 'success');
                closeModal('processModal');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showToast(data.message, 'error');
            }
        } catch (error) {
            console.error('Error processing PRF:', error);
            this.showToast('Error processing PRF request', 'error');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    confirmBulkDelete() {
        if (this.selectedPRFs.size === 0) {
            this.showToast('No PRF requests selected', 'warning');
            return;
        }

        document.getElementById('deleteCount').textContent = 
            `${this.selectedPRFs.size} PRF request(s) will be permanently deleted.`;
        
        openModal('confirmDeleteModal');
    }

    async executeBulkDelete() {
        const deleteBtn = document.querySelector('#confirmDeleteModal .btn-error');
        const originalText = deleteBtn.innerHTML;

        deleteBtn.innerHTML = '<span class="loading-spinner"></span> Deleting...';
        deleteBtn.disabled = true;

        try {
            const response = await fetch('/prf/admin/bulk-delete/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    prf_ids: Array.from(this.selectedPRFs)
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showToast(data.message, 'success');
                closeModal('confirmDeleteModal');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showToast(data.message, 'error');
            }
        } catch (error) {
            console.error('Error deleting PRFs:', error);
            this.showToast('Error deleting PRF requests', 'error');
        } finally {
            deleteBtn.innerHTML = originalText;
            deleteBtn.disabled = false;
        }
    }

    showToast(message, type = 'info') {
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        const toast = document.createElement('div');
        toast.className = 'toast';
        
        let icon = 'info-circle';
        let bgColor = 'var(--primary-color)';
        
        if (type === 'success') {
            icon = 'check-circle';
            bgColor = 'var(--success-color)';
        } else if (type === 'error') {
            icon = 'exclamation-circle';
            bgColor = 'var(--error-color)';
        } else if (type === 'warning') {
            icon = 'exclamation-triangle';
            bgColor = 'var(--warning-color)';
        }
        
        toast.style.cssText = `
            background: ${bgColor};
            color: white;
            border: none;
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px; color: white;">
                <i class="fas fa-${icon}" style="font-size: 1.1rem; opacity: 0.9; color: white;"></i>
                <span style="flex: 1; color: white; font-weight: 500;">${message}</span>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 4000);
    }
}

function updateChartPeriod(period) {
    if (window.adminDashboard) {
        window.adminDashboard.updateChartPeriod(period);
    }
}

function switchChartType(type) {
    const buttons = document.querySelectorAll('.chart-type-btn');
    const filterContainer = document.querySelector('.chart-type-filters');
    buttons.forEach(btn => btn.classList.remove('active'));
    const activeBtn = event.target.closest('.chart-type-btn');
    activeBtn.classList.add('active');

    // Slider effect
    if (filterContainer) {
        filterContainer.classList.remove('slide-0', 'slide-1', 'has-active');
        const index = Array.from(buttons).indexOf(activeBtn);
        filterContainer.classList.add('has-active', `slide-${index}`);
    }

    window.adminDashboard.currentChartType = type;

    // Update chart type and stacking
    if (window.adminDashboard.chart) {
        window.adminDashboard.chart.config.type = type;

        // Update dataset properties for the new chart type
        window.adminDashboard.chart.data.datasets.forEach(dataset => {
            if (type === 'line') {
                dataset.backgroundColor = `${dataset.borderColor}20`;
                dataset.fill = true;
                dataset.tension = 0.4;
                dataset.pointRadius = 4;
                dataset.pointHoverRadius = 6;
                dataset.borderRadius = 0;
            } else {
                dataset.backgroundColor = dataset.borderColor;
                dataset.fill = false;
                dataset.tension = 0;
                dataset.pointRadius = 0;
                dataset.pointHoverRadius = 0;
                dataset.borderRadius = 6;
            }
        });

        // Update stacking for bar charts
        window.adminDashboard.chart.options.scales.x.stacked = type === 'bar';
        window.adminDashboard.chart.options.scales.y.stacked = type === 'bar';

        window.adminDashboard.chart.update('active');
    }
}

async function viewAdminPRFDetail(prfId) {
    try {
        const response = await fetch(`/prf/admin/detail/${prfId}/`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        });

        if (response.ok) {
            const data = await response.json();
            
            document.getElementById('detail-employee').textContent = data.employee;
            document.getElementById('detail-employee-name').textContent = data.employee_name;
            document.getElementById('detail-category').textContent = data.prf_category;
            document.getElementById('detail-type').textContent = data.prf_type;
            document.getElementById('detail-purpose').textContent = data.purpose;
            document.getElementById('detail-control').textContent = data.control_number;
            document.getElementById('detail-status').textContent = data.status;
            document.getElementById('detail-status').className = `status-badge status-${data.status.toLowerCase()}`;
            document.getElementById('detail-remarks').textContent = data.admin_remarks || 'No remarks';
            document.getElementById('detail-created').textContent = data.created_at;
            document.getElementById('detail-updated').textContent = data.updated_at;

            const controlRow = document.getElementById('detail-control-row');
            const remarksRow = document.getElementById('detail-remarks-row');
            
            controlRow.style.display = data.control_number === 'N/A' ? 'none' : 'grid';
            remarksRow.style.display = !data.admin_remarks ? 'none' : 'grid';

            openModal('prfDetailModal');
        } else {
            window.adminDashboard.showToast('Error loading PRF details', 'error');
        }
    } catch (error) {
        console.error('Error fetching PRF details:', error);
        window.adminDashboard.showToast('Error loading PRF details', 'error');
    }
}

function processPRF(prfId) {
    window.adminDashboard.processingPRFId = prfId;
    document.getElementById('processForm').reset();
    openModal('processModal');
}

function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    searchInput.value = '';
    window.adminDashboard.performSearch('');
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    window.adminDashboard.selectAllCheckboxes(selectAllCheckbox.checked);
}

function updateBulkDeleteButton() {
    window.adminDashboard.updateBulkDeleteButton();
}

function confirmBulkDelete() {
    window.adminDashboard.confirmBulkDelete();
}

function executeBulkDelete() {
    window.adminDashboard.executeBulkDelete();
}

function clearFilters() {
    window.adminDashboard.clearFilters();
}

function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    modal.style.display = 'flex';
    requestAnimationFrame(() => {
        modal.classList.add('show');
        modal.classList.remove('closing');
    });
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    modal.classList.add('closing');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.classList.remove('closing');
        modal.style.display = 'none';
    }, 200);
}

document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminPRFDashboard();

    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal-overlay')) {
            const modal = e.target.closest('.modal');
            if (modal) closeModal(modal.id);
        }
    });

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) closeModal(openModal.id);
        }
    });

    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                if (window.adminDashboard.chart && window.chartData) {
                    const data = JSON.parse(window.chartData);
                    window.adminDashboard.createChart(
                        document.getElementById('prfChart'), 
                        data, 
                        isDark
                    );
                }
            }
        });
    });
    
    observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
    });
});