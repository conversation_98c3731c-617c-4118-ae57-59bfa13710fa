{% extends "main.html" %}
{% load static %}

{% block title %}REPConnect - PR-Form{% endblock title %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/prf/prf.css' %}">
{% endblock %}

{% block content %}
    <div class="page-content" id="page-content">
        <header class="page-header">
            <div class="page-header-content">
                <h2>PRF Administration</h2>
                <p>Manage and process personnel request forms</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-outline" onclick="openModal('exportModal')">
                    <i class="fas fa-download"></i>
                    Export PRFs
                </button>
                <button class="btn btn-error" id="bulkDeleteBtn" onclick="confirmBulkDelete()" style="display: none;">
                    <i class="fas fa-trash"></i>
                    Delete Selected
                </button>
            </div>
        </header>

        <div class="dashboard-stats">
            <div class="modern-stats-grid">
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon blue">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Total Requests</div>
                    <div class="modern-stat-value">{{ total_requests }}</div>
                    <div class="modern-stat-change {% if total_requests_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if total_requests_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ total_requests_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon orange">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Pending Requests</div>
                    <div class="modern-stat-value">{{ pending_requests }}</div>
                    <div class="modern-stat-change {% if pending_requests_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if pending_requests_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ pending_requests_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Approved Requests</div>
                    <div class="modern-stat-value">{{ approved_requests }}</div>
                    <div class="modern-stat-change {% if approved_requests_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if approved_requests_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ approved_requests_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
                <div class="modern-stat-card">
                    <div class="modern-stat-header">
                        <div class="modern-stat-icon red">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                    <div class="modern-stat-label">Disapproved Requests</div>
                    <div class="modern-stat-value">{{ disapproved_requests }}</div>
                    <div class="modern-stat-change {% if disapproved_requests_positive %}positive{% else %}negative{% endif %}">
                        <span class="modern-stat-change-icon">
                            <i class="fas {% if disapproved_requests_positive %}fa-arrow-up{% else %}fa-arrow-down{% endif %}"></i>
                        </span>
                        {{ disapproved_requests_percent }}%
                        <span class="modern-stat-change-text">vs prev. month</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card" style="margin-bottom: var(--space-md);">
            <div class="chart-header">
                <h3>PRF Requests Overview</h3>
                <div class="chart-controls">
                    <div class="chart-filters">
                        <button class="filter-btn {% if request.GET.period == 'week' or not request.GET.period %}active{% endif %}" 
                                onclick="updateChartPeriod('week')">This Week</button>
                        <button class="filter-btn {% if request.GET.period == 'month' %}active{% endif %}" 
                                onclick="updateChartPeriod('month')">This Month</button>
                        <button class="filter-btn {% if request.GET.period == 'quarter' %}active{% endif %}" 
                                onclick="updateChartPeriod('quarter')">This Quarter</button>
                    </div>
                    <div class="chart-filters">
                        <button class="filter-btn active" onclick="switchChartType('line')">
                            <i class="fas fa-chart-line"></i>
                        </button>
                        <button class="filter-btn" onclick="switchChartType('bar')">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="prfChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="requests-section">
            <div class="section-header">
                <h3>PRF Requests</h3>
                <div class="section-actions">
                    <div class="search-container">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="searchInput" placeholder="Search requests..." 
                                value="{{ search }}" class="search-input">
                            <button class="search-clear" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <button class="btn btn-outline" onclick="openModal('filterModal')">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                </div>
            </div>

            <div class="table-container">
                <table class="data-table" id="prfTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>Employee</th>
                            <th>PRF Type</th>
                            <th>Purpose</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prf in page_obj %}
                        <tr>
                            <td>
                                <input type="checkbox" class="row-checkbox" value="{{ prf.id }}" 
                                    onchange="updateBulkDeleteButton()">
                            </td>
                            <td>
                                <div class="employee-info">
                                    <strong>{{ prf.employee.username }}</strong>
                                    <small>{{ prf.employee.firstname }} {{ prf.employee.lastname }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="prf-type-info">
                                    <strong>{{ prf.get_prf_type_display }}</strong>
                                    <small>{{ prf.get_prf_category_display }}</small>
                                </div>
                            </td>
                            <td>{{ prf.purpose|truncatechars:50 }}</td>
                            <td>
                                <span class="status-badge status-{{ prf.status }}">
                                    {{ prf.get_status_display }}
                                </span>
                            </td>
                            <td>{{ prf.created_at|date:"M d, Y" }}</td>
                            <td class="table-actions">
                                <button class="btn btn-sm btn-icon btn-primary" 
                                        onclick="viewAdminPRFDetail({{ prf.id }})" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if prf.status == 'pending' %}
                                <button class="btn btn-sm btn-icon btn-success" 
                                        onclick="processPRF({{ prf.id }})" title="Process">
                                    <i class="fas fa-cog"></i>
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="empty-row">No PRF requests found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if page_obj.has_other_pages %}
            <div class="pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}" class="pagination-btn">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="pagination-btn active">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}&{{ request.GET.urlencode }}" class="pagination-btn">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}" class="pagination-btn">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <div id="filterModal" class="modal modal-md">
            <div class="modal-overlay"></div>
            <div class="modal-content modal-sm">
                <div class="modal-header">
                    <h3>Filter PRF Requests</h3>
                    <button class="modal-close" onclick="closeModal('filterModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="get" id="filterForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">PRF Type</label>
                            {{ filter_form.prf_type }}
                        </div>
                        <div class="form-group">
                            <label class="form-label">Start Date</label>
                            {{ filter_form.start_date }}
                        </div>
                        <div class="form-group">
                            <label class="form-label">End Date</label>
                            {{ filter_form.end_date }}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="clearFilters()">Clear</button>
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="exportModal" class="modal modal-md">
            <div class="modal-overlay"></div>
            <div class="modal-content modal-sm">
                <div class="modal-header">
                    <h3>Export PRF Requests</h3>
                    <button class="modal-close" onclick="closeModal('exportModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="exportForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">PRF Type</label>
                            <select name="prf_type" class="form-input">
                                <option value="">All Types</option>
                                {% for value, label in filter_form.prf_type.field.choices %}
                                    {% if value %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Start Date</label>
                            <input type="date" name="start_date" class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">End Date</label>
                            <input type="date" name="end_date" class="form-input">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="closeModal('exportModal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            Export to Excel
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div id="prfDetailModal" class="modal modal-md">
            <div class="modal-overlay"></div>
            <div class="modal-content modal-md">
                <div class="modal-header">
                    <h3>PRF Request Details</h3>
                    <button class="modal-close" onclick="closeModal('prfDetailModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="prf-detail-content">
                        <div class="detail-section">
                            <h4>Employee Information</h4>
                            <div class="detail-row">
                                <label>Username:</label>
                                <span id="detail-employee"></span>
                            </div>
                            <div class="detail-row">
                                <label>Full Name:</label>
                                <span id="detail-employee-name"></span>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4>Request Information</h4>
                            <div class="detail-row">
                                <label>PRF Category:</label>
                                <span id="detail-category"></span>
                            </div>
                            <div class="detail-row">
                                <label>PRF Type:</label>
                                <span id="detail-type"></span>
                            </div>
                            <div class="detail-row">
                                <label>Purpose:</label>
                                <span id="detail-purpose"></span>
                            </div>
                            <div class="detail-row" id="detail-control-row">
                                <label>Control Number:</label>
                                <span id="detail-control"></span>
                            </div>
                            <div class="detail-row">
                                <label>Status:</label>
                                <span id="detail-status" class="status-badge"></span>
                            </div>
                            <div class="detail-row" id="detail-remarks-row">
                                <label>Admin Remarks:</label>
                                <span id="detail-remarks"></span>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4>Timestamps</h4>
                            <div class="detail-row">
                                <label>Submitted:</label>
                                <span id="detail-created"></span>
                            </div>
                            <div class="detail-row">
                                <label>Last Updated:</label>
                                <span id="detail-updated"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="closeModal('prfDetailModal')">Close</button>
                </div>
            </div>
        </div>

        <div id="processModal" class="modal modal-md">
            <div class="modal-overlay"></div>
            <div class="modal-content modal-sm">
                <div class="modal-header">
                    <h3>Process PRF Request</h3>
                    <button class="modal-close" onclick="closeModal('processModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="processForm">
                    {% csrf_token %}
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Decision <span class="required">*</span></label>
                            <select name="status" class="form-input" required>
                                <option value="">Select Decision</option>
                                <option value="approved">Approve</option>
                                <option value="disapproved">Disapprove</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Remarks</label>
                            <textarea name="admin_remarks" class="form-input" rows="3" 
                                    placeholder="Add remarks (optional)..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="closeModal('processModal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">Process Request</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="confirmDeleteModal" class="modal modal-md">
            <div class="modal-overlay"></div>
            <div class="modal-content modal-sm">
                <div class="modal-header">
                    <h3>Confirm Deletion</h3>
                    <button class="modal-close" onclick="closeModal('confirmDeleteModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="confirm-content">
                        <div class="confirm-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <p>Are you sure you want to delete the selected PRF requests? This action cannot be undone.</p>
                        <p id="deleteCount"></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="closeModal('confirmDeleteModal')">Cancel</button>
                    <button class="btn btn-error" onclick="executeBulkDelete()">Delete</button>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    window.chartData = {{ chart_data|safe }};
</script>
    <script src="{% static 'js/prf/admin-prf.js' %}"></script>
{% endblock %}