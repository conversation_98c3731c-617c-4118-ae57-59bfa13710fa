from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
import json

from .models import PRFRequest
from .forms import PRFRequestForm, PRFFilterForm, PRFActionForm
from notification.models import Notification

@login_required
def employee_prf_view(request):
    if request.user.hr_admin:
        return redirect('admin_prf')
    
    user_prfs = PRFRequest.objects.filter(employee=request.user)
    
    context = {
        'prfs': user_prfs,
        'form': PRFRequestForm(),
    }
    return render(request, 'prf/user-prf.html', context)

@login_required
def submit_prf_request(request):
    if request.method == 'POST':
        form = PRFRequestForm(request.POST)
        if form.is_valid():
            prf_request = form.save(commit=False)
            prf_request.employee = request.user
            
            loan_types = ['pagibig_loan', 'sss_loan', 'emergency_loan', 'medical_loan', 'educational_loan', 'coop_loan']
            if prf_request.prf_type in loan_types and not prf_request.control_number:
                return JsonResponse({
                    'success': False,
                    'message': 'Control number is required for this PRF type.'
                })
            
            prf_request.save()
            return JsonResponse({
                'success': True,
                'message': 'PRF request submitted successfully!'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Please fill in all required fields correctly.'
            })
    
    return JsonResponse({'success': False, 'message': 'Invalid request method.'})

@login_required
def get_prf_types(request):
    category = request.GET.get('category')
    if not category:
        return JsonResponse({'types': []})
    
    category_types = {
        'government': [
            ('pagibig_loan', 'PAG-IBIG Loan'),
            ('pagibig_cert_payment', 'PAG-IBIG Certificate of Payment'),
            ('pagibig_cert_contribution', 'PAG-IBIG Certificate of Contribution'),
            ('philhealth_form', 'PHILHEALTH Form'),
            ('sss_loan', 'SSS Loan'),
            ('sss_maternity', 'SSS Maternity Benefits'),
            ('sss_sickness', 'SSS Sickness Benefits'),
            ('bir_form', 'BIR Form (2316/1902)'),
        ],
        'banking': [
            ('rcbc_maintenance', 'RCBC Maintenance Form'),
            ('bank_deposit', 'Bank Deposit'),
        ],
        'hr_payroll': [
            ('payroll_adjustment', 'Payroll Adjustment'),
            ('id_replacement', 'ID Replacement'),
            ('pcoe_compensation', 'PCOE with Compensation'),
            ('certificate_employment', 'Certificate of Employment'),
            ('clearance_form', 'Clearance Form'),
            ('emergency_loan', 'Emergency Loan'),
            ('medical_loan', 'Medical Assistance Loan'),
            ('educational_loan', 'Educational Assistance Loan'),
            ('coop_loan', 'Coop Loan'),
            ('uniform_ppe', 'Uniform / Caps / PPE / T-shirt'),
            ('others', 'Others'),
        ]
    }
    
    types = category_types.get(category, [])
    return JsonResponse({'types': types})

@login_required
def get_prf_detail(request, prf_id):
    prf = get_object_or_404(PRFRequest, id=prf_id, employee=request.user)
    
    return JsonResponse({
        'id': prf.id,
        'prf_category': prf.get_prf_category_display(),
        'prf_type': prf.get_prf_type_display(),
        'purpose': prf.purpose,
        'control_number': prf.control_number or 'N/A',
        'status': prf.get_status_display(),
        'admin_remarks': prf.admin_remarks or 'No remarks',
        'created_at': prf.created_at.strftime('%B %d, %Y at %I:%M %p'),
        'updated_at': prf.updated_at.strftime('%B %d, %Y at %I:%M %p'),
    })

@login_required
def admin_dashboard(request):
    if not request.user.hr_admin:
        return redirect('user_prf')
    
    period = request.GET.get('period', 'month')
    now = timezone.now()
    
    if period == 'week':
        start_date = now - timedelta(days=7)
        title = 'This Week'
    elif period == 'quarter':
        start_date = now - timedelta(days=90)
        title = 'This Quarter'
    else:
        start_date = now - timedelta(days=30)
        title = 'This Month'
    
    prfs = PRFRequest.objects.all().select_related('employee', 'processed_by')
    
    search = request.GET.get('search', '')
    if search:
        prfs = prfs.filter(
            Q(employee__username__icontains=search) |
            Q(prf_type__icontains=search) |
            Q(purpose__icontains=search)
        )
    
    filter_form = PRFFilterForm(request.GET)
    if filter_form.is_valid():
        if filter_form.cleaned_data['prf_type']:
            prfs = prfs.filter(prf_type=filter_form.cleaned_data['prf_type'])
        if filter_form.cleaned_data['start_date']:
            prfs = prfs.filter(created_at__date__gte=filter_form.cleaned_data['start_date'])
        if filter_form.cleaned_data['end_date']:
            prfs = prfs.filter(created_at__date__lte=filter_form.cleaned_data['end_date'])
    
    chart_data = []
    for i in range(7 if period == 'week' else 30 if period == 'month' else 90):
        date = start_date + timedelta(days=i)
        count = prfs.filter(
            created_at__date=date.date()
        ).count()
        chart_data.append({
            'date': date.strftime('%b %d' if period != 'week' else '%a'),
            'count': count
        })
    
    paginator = Paginator(prfs, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    start_of_this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    start_of_prev_month = (start_of_this_month - timedelta(days=1)).replace(day=1)
    end_of_prev_month = start_of_this_month - timedelta(seconds=1)

    def get_count(qs):
        return qs.count()

    total_this = PRFRequest.objects.filter(created_at__gte=start_of_this_month).count()
    total_prev = PRFRequest.objects.filter(created_at__gte=start_of_prev_month, created_at__lt=start_of_this_month).count()
    pending_this = PRFRequest.objects.filter(status='pending', created_at__gte=start_of_this_month).count()
    pending_prev = PRFRequest.objects.filter(status='pending', created_at__gte=start_of_prev_month, created_at__lt=start_of_this_month).count()
    approved_this = PRFRequest.objects.filter(status='approved', created_at__gte=start_of_this_month).count()
    approved_prev = PRFRequest.objects.filter(status='approved', created_at__gte=start_of_prev_month, created_at__lt=start_of_this_month).count()
    disapproved_this = PRFRequest.objects.filter(status='disapproved', created_at__gte=start_of_this_month).count()
    disapproved_prev = PRFRequest.objects.filter(status='disapproved', created_at__gte=start_of_prev_month, created_at__lt=start_of_this_month).count()

    def calc_percent(this, prev):
        if prev == 0:
            return 100 if this > 0 else 0
        return round(((this - prev) / prev) * 100)

    context = {
        'chart_data': json.dumps(chart_data),
        'chart_title': title,
        'page_obj': page_obj,
        'filter_form': filter_form,
        'search': search,
        'total_requests': PRFRequest.objects.count(),
        'pending_requests': PRFRequest.objects.filter(status='pending').count(),
        'approved_requests': PRFRequest.objects.filter(status='approved').count(),
        'disapproved_requests': PRFRequest.objects.filter(status='disapproved').count(),
        'total_requests_percent': abs(calc_percent(total_this, total_prev)),
        'total_requests_positive': total_this >= total_prev,
        'pending_requests_percent': abs(calc_percent(pending_this, pending_prev)),
        'pending_requests_positive': pending_this >= pending_prev,
        'approved_requests_percent': abs(calc_percent(approved_this, approved_prev)),
        'approved_requests_positive': approved_this >= approved_prev,
        'disapproved_requests_percent': abs(calc_percent(disapproved_this, disapproved_prev)),
        'disapproved_requests_positive': disapproved_this >= disapproved_prev,
    }
    return render(request, 'prf/admin-prf.html', context)

@login_required
def process_prf_request(request, prf_id):
    if not request.user.hr_admin:
        return JsonResponse({'success': False, 'message': 'Unauthorized'})
    
    prf = get_object_or_404(PRFRequest, id=prf_id)
    
    if request.method == 'POST':
        form = PRFActionForm(request.POST, instance=prf)
        if form.is_valid():
            prf_request = form.save(commit=False)
            prf_request.processed_by = request.user
            prf_request.save()
            
            Notification.objects.create(
                title=f'PRF Request {prf_request.get_status_display()}',
                message=f'Your PRF request for {prf_request.get_prf_type_display()} has been {prf_request.get_status_display().lower()}.',
                notification_type=prf_request.status,
                sender=request.user,
                recipient=prf_request.employee,
                module='prf'
            )
            
            return JsonResponse({
                'success': True,
                'message': f'PRF request {prf_request.get_status_display().lower()} successfully!'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Please fill in all required fields.'
            })
    
    return JsonResponse({'success': False, 'message': 'Invalid request method.'})

@login_required
def get_admin_prf_detail(request, prf_id):
    if not request.user.hr_admin:
        return JsonResponse({'success': False, 'message': 'Unauthorized'})
    
    prf = get_object_or_404(PRFRequest, id=prf_id)
    
    return JsonResponse({
        'id': prf.id,
        'employee': prf.employee.username,
        'employee_name': f"{prf.employee.firstname} {prf.employee.lastname}",
        'prf_category': prf.get_prf_category_display(),
        'prf_type': prf.get_prf_type_display(),
        'purpose': prf.purpose,
        'control_number': prf.control_number or 'N/A',
        'status': prf.get_status_display(),
        'admin_remarks': prf.admin_remarks or '',
        'created_at': prf.created_at.strftime('%B %d, %Y at %I:%M %p'),
        'updated_at': prf.updated_at.strftime('%B %d, %Y at %I:%M %p'),
    })

@login_required
def export_prfs(request):
    if not request.user.hr_admin:
        return redirect('admin_prf')
    
    prfs = PRFRequest.objects.all().select_related('employee', 'processed_by')
    
    prf_type = request.GET.get('prf_type')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if prf_type:
        prfs = prfs.filter(prf_type=prf_type)
    if start_date:
        prfs = prfs.filter(created_at__date__gte=start_date)
    if end_date:
        prfs = prfs.filter(created_at__date__lte=end_date)
    
    wb = Workbook()
    ws = wb.active
    ws.title = "PRF Requests"
    
    headers = [
        'ID', 'Employee', 'Employee Name', 'Category', 'PRF Type', 
        'Purpose', 'Control Number', 'Status', 'Admin Remarks', 
        'Processed By', 'Created At', 'Updated At'
    ]
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')
    
    for row, prf in enumerate(prfs, 2):
        ws.cell(row=row, column=1, value=prf.id)
        ws.cell(row=row, column=2, value=prf.employee.username)
        ws.cell(row=row, column=3, value=f"{prf.employee.firstname} {prf.employee.lastname}")
        ws.cell(row=row, column=4, value=prf.get_prf_category_display())
        ws.cell(row=row, column=5, value=prf.get_prf_type_display())
        ws.cell(row=row, column=6, value=prf.purpose)
        ws.cell(row=row, column=7, value=prf.control_number or 'N/A')
        ws.cell(row=row, column=8, value=prf.get_status_display())
        ws.cell(row=row, column=9, value=prf.admin_remarks or 'No remarks')
        ws.cell(row=row, column=10, value=prf.processed_by.username if prf.processed_by else 'Not processed')
        ws.cell(row=row, column=11, value=prf.created_at.strftime('%Y-%m-%d %H:%M:%S'))
        ws.cell(row=row, column=12, value=prf.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
    
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename=prf_requests_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    wb.save(response)
    return response

@login_required
def bulk_delete_prfs(request):
    if not request.user.hr_admin:
        return JsonResponse({'success': False, 'message': 'Unauthorized'})
    
    if request.method == 'POST':
        data = json.loads(request.body)
        prf_ids = data.get('prf_ids', [])
        
        if not prf_ids:
            return JsonResponse({'success': False, 'message': 'No PRFs selected'})
        
        deleted_count = PRFRequest.objects.filter(id__in=prf_ids).delete()[0]
        
        return JsonResponse({
            'success': True,
            'message': f'{deleted_count} PRF(s) deleted successfully!'
        })
    
    return JsonResponse({'success': False, 'message': 'Invalid request method.'})