.page-header {
    margin: var(--space-md) 0px var(--space-sm) 0px ;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-description {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-lg);
}

.prf-stats,
.dashboard-stats {
    margin-bottom: var(--space-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-lg);
}

.prf-requests-section,
.requests-section {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    box-shadow: var(--shadow-sm);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.section-header h2 {
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.prf-requests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-lg);
}

.prf-request-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.prf-request-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: var(--primary-color);
}

.prf-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-md);
}

.prf-type h3 {
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.prf-category {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.prf-card-body {
    margin-bottom: var(--space-md);
}

.prf-purpose {
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
    line-height: 1.5;
}

.prf-control {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.prf-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-md);
    border-top: 1px solid var(--border-color);
}

.prf-date {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.empty-state {
    text-align: center;
    padding: var(--space-2xl) var(--space-xl);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--bg-tertiary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    color: var(--text-muted);
    font-size: var(--font-size-3xl);
}

.empty-state h3 {
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.required {
    color: var(--error-color);
}

.chart-section {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-sm);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-md);
    border-bottom: 2px solid var(--border-color);
}

.chart-header h2 {
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: var(--space-lg);
    align-items: center;
}

.chart-filters {
    display: flex;
    gap: var(--space-xs);
    background: var(--bg-secondary);
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.filter-btn {
    background: transparent;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.chart-type-toggle {
    display: flex;
    gap: var(--space-xs);
    background: var(--bg-secondary);
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.chart-type-btn {
    background: transparent;
    border: none;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-type-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.chart-type-btn.active {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.chart-container {
    position: relative;
    height: 300px;
    margin-top: var(--space-lg);
}

.search-container {
    position: relative;
}

.search-box {
    position: relative;
    min-width: 300px;
}

.search-input {
    width: 100%;
    padding: var(--space-sm) var(--space-md) var(--space-sm) calc(var(--space-md) * 2.5);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface);
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-clear {
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-muted);
    opacity: 0;
    transition: all var(--transition-fast);
}

.search-input:not(:placeholder-shown) ~ .search-clear {
    opacity: 1;
}

.search-clear:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.employee-info strong {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
}

.employee-info small {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.prf-type-info strong {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
}

.prf-type-info small {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.status-badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
    white-space: nowrap;
}

.prf-type-cell {
    color: var(--text-primary);
}

.prf-category-badge {
    display: inline-block;
    padding: var(--space-xs) var(--space-sm);
    background: var(--primary-color);
    color: var(--text-inverse);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.prf-purpose-cell {
    max-width: 300px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.control-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.1);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
}

.no-control {
    color: var(--text-muted);
    font-style: italic;
}

.date-cell {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.date-cell i {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-sm);
    margin-top: var(--space-lg);
}

.prf-detail-content {
    display: grid;
    gap: var(--space-md);
}

.detail-section {
    margin-bottom: var(--space-xl);
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid var(--border-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.detail-row {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--space-md);
    padding: var(--space-sm) 0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row label {
    font-weight: 600;
    color: var(--text-secondary);
}

.detail-row span {
    color: var(--text-primary);
}

.confirm-content {
    text-align: center;
    padding: var(--space-lg) 0;
}

.confirm-icon {
    width: 60px;
    height: 60px;
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-lg);
    font-size: var(--font-size-2xl);
}

.confirm-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-md);
}

#deleteCount {
    font-weight: 600;
    color: var(--text-primary);
}

@media (max-width: 1200px) {
    .chart-controls {
        flex-direction: column;
        gap: var(--space-md);
        align-items: stretch;
    }
    
    .chart-filters {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: var(--space-lg);
        align-items: stretch;
    }
    
    .section-actions {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .search-box {
        min-width: auto;
        width: 100%;
    }
    
    .chart-controls {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .prf-requests-grid {
        grid-template-columns: 1fr;
    }
    
    .prf-card-footer {
        flex-direction: column;
        gap: var(--space-sm);
        align-items: stretch;
    }
    
    .prf-purpose-cell {
        max-width: 200px;
    }
    
    .detail-row {
        grid-template-columns: 1fr;
        gap: var(--space-xs);
    }
}

@media (max-width: 480px) {
    
    .prf-requests-section,
    .requests-section,
    .chart-section {
        padding: var(--space-lg);
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .chart-container {
        height: 250px;
    }

}