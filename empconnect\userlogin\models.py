from django.db import models
from django.contrib.auth.models import AbstractUser

class EmployeeLogin(AbstractUser):
    STATUS_TYPE = [
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
    ]
    
    avatar = models.ImageField(upload_to='profile/', null=True, blank=True, default='profile/avatar.svg') 
    idnumber = models.CharField(max_length=10, null=True)
    username = models.CharField(max_length=20, unique=True, null=True)
    firstname = models.CharField(max_length=20, null=True)
    lastname = models.CharField(max_length=20, null=True)
    email = models.EmailField(unique=True, blank=True)
    active = models.BooleanField(default=True)
    wire_admin = models.BooleanField(default=False)
    clinic_admin = models.BooleanField(default=False)
    iad_admin = models.BooleanField(default=False)
    accounting_admin = models.BooleanField(default=False)
    hr_admin = models.BooleanField(default=False)
    hr_manager = models.BooleanField(default=False)
    mis_admin = models.BooleanField(default=False)
    status = models.CharField(max_length=10, choices=STATUS_TYPE, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = []

    def save(self, *args, **kwargs):
        if not self.avatar:
            self.avatar = 'profile/avatar.svg'
        super().save(*args, **kwargs)

